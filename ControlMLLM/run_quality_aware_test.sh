#!/bin/bash

# Quality-Aware Visual Anchors Optimization - 测试脚本
# 解决优化效果与生成质量脱节的问题

echo "========================================"
echo "🔬 Quality-Aware Optimization - 质量感知优化测试"
echo "========================================"
echo "解决关键问题："
echo "- 防止优化过程破坏文本生成质量"
echo "- 平衡attention优化与语义连贯性"
echo "- 使用保守参数确保稳定性"
echo "- 实时质量监控和回退机制"
echo "========================================"

# ==========================================
# 🎯 质量感知配置参数
# ==========================================

# 模型和数据路径
MODEL_PATH="/data1/mrwu_tmp/haoyuan/models/llava-1.5-7b-hf"
DATA_PATH="/data1/mrwu_tmp/haoyuan/data/ROC/LVIS"
QUESTION_FILE="/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json"
OUTPUT_DIR="outputs/quality_aware"

# CUDA设备配置
export CUDA_VISIBLE_DEVICES=6,7
export TOKENIZERS_PARALLELISM=false

# ==========================================
# 📊 保守优化参数（基于问题分析）
# ==========================================

VISUAL_PROMPT="Box"
T=5                           # 适中的优化步数
ALPHA=400                     # 保持attention loss权重
LR=0.01                       # 保守学习率（降低100倍）
BETA=0.9                      # 保守EMA参数
GRAD_CLIP=0.1                 # 严格梯度裁剪（降低20倍）

# 质量控制参数
QUALITY_THRESHOLD=0.7         # 最低质量阈值
QUALITY_WEIGHT=0.3            # 质量loss权重
MAX_PERTURBATION_NORM=0.1     # 最大扰动幅度限制

# Anchor配置
N_SPATIAL=2
N_SEMANTIC=2
N_RELATIONAL=2
ANCHOR_INIT_STD=0.001         # 极小的初始化标准差

# 功能开关
USE_EMA="--use_ema"
VERBOSE="--verbose"

echo "🔧 质量感知配置:"
echo "  学习率: $LR (保守设置，降低100倍)"
echo "  梯度裁剪: $GRAD_CLIP (严格限制，降低20倍)"
echo "  质量阈值: $QUALITY_THRESHOLD"
echo "  最大扰动: $MAX_PERTURBATION_NORM"
echo "  EMA参数: $BETA (保守设置)"

# ==========================================
# 📁 输出配置
# ==========================================

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="${OUTPUT_DIR}/quality_aware_lr${LR}_clip${GRAD_CLIP}_${TIMESTAMP}.json"

mkdir -p $OUTPUT_DIR

echo "📁 输出文件: $OUTPUT_FILE"

# ==========================================
# 🚀 执行质量感知优化
# ==========================================

echo ""
echo "========================================"
echo "🚀 开始质量感知优化测试..."
echo "========================================"
echo "预期效果："
echo "  ✅ 保持文本生成质量（无乱码）"
echo "  ✅ 实现适度的attention优化"
echo "  ✅ 稳定的梯度更新过程"
echo "  ✅ 质量-优化效果平衡"
echo "========================================"

START_TIME=$(date +%s)

# 运行质量感知优化
python controlmllm/task/ROC/llava_roc_quality_aware.py \
    --model_path $MODEL_PATH \
    --data_path $DATA_PATH \
    --question_file $QUESTION_FILE \
    --answers_file $OUTPUT_FILE \
    --visual_prompt $VISUAL_PROMPT \
    --T $T \
    --alpha $ALPHA \
    --lr $LR \
    --beta $BETA \
    --grad_clip $GRAD_CLIP \
    --quality_threshold $QUALITY_THRESHOLD \
    --quality_weight $QUALITY_WEIGHT \
    --max_perturbation_norm $MAX_PERTURBATION_NORM \
    --n_spatial_anchors $N_SPATIAL \
    --n_semantic_anchors $N_SEMANTIC \
    --n_relational_anchors $N_RELATIONAL \
    --anchor_init_std $ANCHOR_INIT_STD \
    $USE_EMA \
    $VERBOSE

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "========================================"
echo "✅ 质量感知优化测试完成！"
echo "========================================"
echo "📊 执行统计:"
echo "  耗时: ${DURATION}秒"
echo "  结果文件: $OUTPUT_FILE"
echo "  配置: lr=$LR, clip=$GRAD_CLIP, quality_threshold=$QUALITY_THRESHOLD"
echo "========================================"

# ==========================================
# 📈 结果分析和验证
# ==========================================

echo ""
echo "💡 结果验证建议:"
echo "  1. 检查生成文本是否无乱码和重复"
echo "  2. 验证质量分数是否保持在阈值以上"
echo "  3. 确认loss是否有适度下降"
echo "  4. 观察梯度norm是否稳定"
echo ""
echo "🔍 快速查看结果:"
echo "  head -3 $OUTPUT_FILE | jq '.'"
echo ""
echo "📊 质量统计分析:"
echo "  grep 'quality_improvement' $OUTPUT_FILE"
echo ""
echo "🎯 成功标准:"
echo "  ✅ 质量改进 >= 0 (无质量退化)"
echo "  ✅ Loss下降 > 0 (有优化效果)"
echo "  ✅ 无乱码文本生成"
echo "  ✅ 梯度norm稳定 (< 10倍变化)"
echo "========================================"

# ==========================================
# 🧪 可选：参数对比实验
# ==========================================

echo ""
echo "🔬 是否运行参数对比实验? (y/n)"
echo "  将测试不同保守程度的参数配置"
read -r response

if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo ""
    echo "========================================"
    echo "🧪 运行参数对比实验"
    echo "========================================"
    
    # 实验1: 更保守的参数
    echo "实验1: 极保守参数 (lr=0.001, clip=0.01)"
    python controlmllm/task/ROC/llava_roc_quality_aware.py \
        --model_path $MODEL_PATH \
        --data_path $DATA_PATH \
        --question_file $QUESTION_FILE \
        --answers_file "${OUTPUT_DIR}/ultra_conservative_${TIMESTAMP}.json" \
        --visual_prompt $VISUAL_PROMPT \
        --T $T \
        --alpha $ALPHA \
        --lr 0.001 \
        --beta 0.95 \
        --grad_clip 0.01 \
        --quality_threshold $QUALITY_THRESHOLD \
        --quality_weight $QUALITY_WEIGHT \
        --max_perturbation_norm 0.05 \
        --n_spatial_anchors $N_SPATIAL \
        --n_semantic_anchors $N_SEMANTIC \
        --n_relational_anchors $N_RELATIONAL \
        --anchor_init_std 0.0001 \
        $USE_EMA $VERBOSE
    
    # 实验2: 适中的参数
    echo "实验2: 适中参数 (lr=0.05, clip=0.5)"
    python controlmllm/task/ROC/llava_roc_quality_aware.py \
        --model_path $MODEL_PATH \
        --data_path $DATA_PATH \
        --question_file $QUESTION_FILE \
        --answers_file "${OUTPUT_DIR}/moderate_${TIMESTAMP}.json" \
        --visual_prompt $VISUAL_PROMPT \
        --T $T \
        --alpha $ALPHA \
        --lr 0.05 \
        --beta 0.8 \
        --grad_clip 0.5 \
        --quality_threshold 0.6 \
        --quality_weight $QUALITY_WEIGHT \
        --max_perturbation_norm 0.2 \
        --n_spatial_anchors $N_SPATIAL \
        --n_semantic_anchors $N_SEMANTIC \
        --n_relational_anchors $N_RELATIONAL \
        --anchor_init_std 0.005 \
        $USE_EMA $VERBOSE
        
    echo ""
    echo "========================================"
    echo "🎯 对比实验完成！"
    echo "========================================"
    echo "📊 生成的对比文件:"
    echo "  主要结果: $OUTPUT_FILE"
    echo "  极保守: ${OUTPUT_DIR}/ultra_conservative_${TIMESTAMP}.json"
    echo "  适中参数: ${OUTPUT_DIR}/moderate_${TIMESTAMP}.json"
    echo ""
    echo "💡 分析建议:"
    echo "  1. 对比不同配置的质量改进情况"
    echo "  2. 找到质量-优化效果的最佳平衡点"
    echo "  3. 选择最稳定的参数配置用于全量处理"
    echo "========================================"
fi

echo ""
echo "🎉 Quality-Aware Optimization 测试完成！"
echo "📈 已实施质量感知的优化策略"
echo "🔍 请检查结果文件验证质量改进效果"
