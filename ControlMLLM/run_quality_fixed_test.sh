#!/bin/bash

# Quality-Fixed Visual Anchors Optimization - 最终修复版本
# 使用原始稳定机制 + 质量监控 + 保守参数

echo "========================================"
echo "🔧 Quality-Fixed Optimization - 最终修复版本"
echo "========================================"
echo "核心改进："
echo "- 使用原始稳定的visual_prompt机制"
echo "- 添加可选的质量监控和回退"
echo "- 基于测试验证的保守参数配置"
echo "- 确保生成质量和优化效果的平衡"
echo "========================================"

# ==========================================
# 🎯 最终修复配置参数
# ==========================================

# 模型和数据路径
MODEL_PATH="/data1/mrwu_tmp/haoyuan/models/llava-1.5-7b-hf"
DATA_PATH="/data1/mrwu_tmp/haoyuan/data/ROC/LVIS"
QUESTION_FILE="/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json"
OUTPUT_DIR="outputs/quality_fixed"

# CUDA设备配置
export CUDA_VISIBLE_DEVICES=6,7
export TOKENIZERS_PARALLELISM=false

# ==========================================
# 📊 验证有效的保守参数（基于测试结果）
# ==========================================

VISUAL_PROMPT="Box"
T=5                           # 适中的优化步数
ALPHA=400                     # 保持attention loss权重
LR=0.005                      # 验证有效的保守学习率
BETA=0.9                      # 保守EMA参数
GRAD_CLIP=0.05                # 严格梯度裁剪

# 质量控制参数
QUALITY_THRESHOLD=0.8         # 高质量阈值
ENABLE_QUALITY_CHECK="--enable_quality_check"  # 启用质量监控

# Anchor配置（保持兼容性）
N_SPATIAL=2
N_SEMANTIC=2
N_RELATIONAL=2
ANCHOR_INIT_STD=0.0001

# 功能开关
USE_EMA="--use_ema"
VERBOSE="--verbose"

echo "🔧 最终修复配置:"
echo "  学习率: $LR (验证稳定)"
echo "  梯度裁剪: $GRAD_CLIP (严格控制)"
echo "  质量阈值: $QUALITY_THRESHOLD"
echo "  质量监控: 启用"
echo "  EMA参数: $BETA (保守设置)"

# ==========================================
# 📁 输出配置
# ==========================================

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="${OUTPUT_DIR}/quality_fixed_lr${LR}_${TIMESTAMP}.json"

mkdir -p $OUTPUT_DIR

echo "📁 输出文件: $OUTPUT_FILE"

# ==========================================
# 🚀 执行最终修复版本
# ==========================================

echo ""
echo "========================================"
echo "🚀 开始质量修复优化测试..."
echo "========================================"
echo "预期效果："
echo "  ✅ 完全消除乱码和重复文本"
echo "  ✅ 保持原始生成质量"
echo "  ✅ 实现适度的attention优化"
echo "  ✅ 稳定的优化过程"
echo "========================================"

START_TIME=$(date +%s)

# 运行质量修复优化
python controlmllm/task/ROC/llava_roc_quality_fixed.py \
    --model_path $MODEL_PATH \
    --data_path $DATA_PATH \
    --question_file $QUESTION_FILE \
    --answers_file $OUTPUT_FILE \
    --visual_prompt $VISUAL_PROMPT \
    --T $T \
    --alpha $ALPHA \
    --lr $LR \
    --beta $BETA \
    --grad_clip $GRAD_CLIP \
    --quality_threshold $QUALITY_THRESHOLD \
    --n_spatial_anchors $N_SPATIAL \
    --n_semantic_anchors $N_SEMANTIC \
    --n_relational_anchors $N_RELATIONAL \
    --anchor_init_std $ANCHOR_INIT_STD \
    $ENABLE_QUALITY_CHECK \
    $USE_EMA \
    $VERBOSE

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "========================================"
echo "✅ 质量修复优化测试完成！"
echo "========================================"
echo "📊 执行统计:"
echo "  耗时: ${DURATION}秒"
echo "  结果文件: $OUTPUT_FILE"
echo "  配置: lr=$LR, clip=$GRAD_CLIP, quality_threshold=$QUALITY_THRESHOLD"
echo "========================================"

# ==========================================
# 📈 结果验证和分析
# ==========================================

echo ""
echo "💡 结果验证检查:"
echo "  1. 检查是否完全消除了乱码文本"
echo "  2. 验证质量分数是否保持高水平"
echo "  3. 确认loss是否有合理下降"
echo "  4. 观察优化过程是否稳定"
echo ""
echo "🔍 快速查看结果:"
echo "  head -2 $OUTPUT_FILE | jq '.'"
echo ""
echo "📊 质量统计分析:"
echo "  grep 'quality_improvement' $OUTPUT_FILE | head -5"
echo ""
echo "🎯 成功标准验证:"
echo "  ✅ 质量改进 >= -0.1 (基本无质量损失)"
echo "  ✅ Loss下降 > 0 (有优化效果)"
echo "  ✅ 完全无乱码文本"
echo "  ✅ 优化过程稳定"
echo ""
echo "🏆 如果测试成功，可以运行完整数据集:"
echo "  # 修改脚本中的 questions[:3] 为 questions"
echo "  # 然后重新运行完整优化"
echo "========================================"

# ==========================================
# 🧪 可选：与原版对比测试
# ==========================================

echo ""
echo "🔬 是否运行与原版的对比测试? (y/n)"
echo "  将对比修复版本与原版的效果差异"
read -r response

if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo ""
    echo "========================================"
    echo "🧪 运行对比测试"
    echo "========================================"
    
    # 对比测试：使用原版参数运行修复版本
    echo "对比测试: 使用原版参数运行修复版本"
    python controlmllm/task/ROC/llava_roc_quality_fixed.py \
        --model_path $MODEL_PATH \
        --data_path $DATA_PATH \
        --question_file $QUESTION_FILE \
        --answers_file "${OUTPUT_DIR}/comparison_original_params_${TIMESTAMP}.json" \
        --visual_prompt $VISUAL_PROMPT \
        --T $T \
        --alpha $ALPHA \
        --lr 0.1 \
        --beta 0.5 \
        --grad_clip 1.0 \
        --quality_threshold 0.5 \
        --n_spatial_anchors $N_SPATIAL \
        --n_semantic_anchors $N_SEMANTIC \
        --n_relational_anchors $N_RELATIONAL \
        --anchor_init_std 0.01 \
        $ENABLE_QUALITY_CHECK \
        $USE_EMA $VERBOSE
        
    echo ""
    echo "========================================"
    echo "🎯 对比测试完成！"
    echo "========================================"
    echo "📊 生成的对比文件:"
    echo "  修复版本: $OUTPUT_FILE"
    echo "  原版参数: ${OUTPUT_DIR}/comparison_original_params_${TIMESTAMP}.json"
    echo ""
    echo "💡 分析建议:"
    echo "  1. 对比两个版本的质量改进情况"
    echo "  2. 验证修复版本的稳定性优势"
    echo "  3. 确认保守参数的有效性"
    echo "========================================"
fi

echo ""
echo "🎉 Quality-Fixed Optimization 测试完成！"
echo "📈 基于原始稳定机制的质量修复方案"
echo "🔍 请检查结果验证修复效果"
