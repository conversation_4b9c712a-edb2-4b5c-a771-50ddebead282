#!/usr/bin/env python3
"""
Test-time Visual Anchor Embeddings Optimization for ControlMLLM
基于ATPrompt启发的多层次visual anchor优化方法
"""
import sys, os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from PIL import Image
import argparse
from tqdm import tqdm
from transformers import AutoProcessor, LlavaForConditionalGeneration, BitsAndBytesConfig
import torchvision.transforms as transforms
import cv2
import copy

from utils import compute_ca_loss, show_image_relevance
from visualizer import get_local
import clip

# Activate get_local and load CLIP
get_local.activate()
_, preprocess = clip.load("ViT-B/32", device='cpu', jit=False)

def parse_args():
    parser = argparse.ArgumentParser(description="Visual Anchor Embeddings Optimization for ControlMLLM")
    
    # Model and data paths
    parser.add_argument('--model_path', type=str, default="pretrained_models/llava-1.5-7b-hf")
    parser.add_argument('--data_path', type=str, default="data/ROC/LVIS")
    parser.add_argument('--question_file', type=str, default='data/ROC/question_roc.json')
    parser.add_argument('--answers_file', type=str, default='outputs/llava_roc_visual_anchors.json')
    
    # Visual prompt settings
    parser.add_argument('--visual_prompt', type=str, default='Box', choices=['Box', 'Mask', 'Scribble', 'Point'])
    parser.add_argument('--H', type=int, default=24)
    parser.add_argument('--W', type=int, default=24)
    parser.add_argument('--n_px', type=int, default=224)
    
    # Optimization parameters
    parser.add_argument('--T', type=int, default=15, help='Number of optimization steps')
    parser.add_argument('--alpha', type=float, default=400, help='Loss weight')
    parser.add_argument('--lr', type=float, default=0.1, help='Learning rate for anchor optimization (using manual gradient descent like llava_roc.py)')
    parser.add_argument('--beta', type=float, default=0.7, help='EMA weight for anchor updates')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='Gradient clip threshold')
    
    # Visual Anchor settings
    parser.add_argument('--n_spatial_anchors', type=int, default=2, help='Number of spatial anchor embeddings')
    parser.add_argument('--n_semantic_anchors', type=int, default=2, help='Number of semantic anchor embeddings')
    parser.add_argument('--n_relational_anchors', type=int, default=2, help='Number of relational anchor embeddings')
    parser.add_argument('--anchor_init_std', type=float, default=0.01, help='Initialization std for anchors')
    
    # Optimization strategy
    parser.add_argument('--use_ema', action='store_true', help='Use EMA for anchor updates')
    parser.add_argument('--early_stop', action='store_true', help='Enable early stopping')
    parser.add_argument('--loss_threshold', type=float, default=0.001, help='Early stop loss threshold')
    
    # Other settings
    parser.add_argument('--show_att', action='store_true', help='Show attention maps')
    parser.add_argument('--resume', action='store_true', help='Resume from checkpoint')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    return parser.parse_args()


class AttributeAwareVisualAnchorOptimizer(nn.Module):
    """
    Attribute-Aware Visual Anchor Embeddings Optimizer
    基于ATPrompt思想的属性感知视觉锚点优化器
    包含三个属性层次: spatial, semantic, relational + 属性连接器
    """
    def __init__(self, embed_dim=4096, n_spatial=2, n_semantic=2, n_relational=2, init_std=0.01):
        super().__init__()

        self.embed_dim = embed_dim
        self.n_spatial = n_spatial
        self.n_semantic = n_semantic
        self.n_relational = n_relational

        # ATPrompt启发：属性感知的锚点设计
        # Level 1: Spatial Attribute Anchors - 空间属性（位置、大小、方向）
        self.spatial_attr_anchors = nn.Parameter(
            torch.randn(n_spatial, embed_dim) * init_std
        )

        # Level 2: Semantic Attribute Anchors - 语义属性（物体类别、功能）
        self.semantic_attr_anchors = nn.Parameter(
            torch.randn(n_semantic, embed_dim) * init_std
        )

        # Level 3: Relational Attribute Anchors - 关系属性（包含、相邻、比较）
        self.relational_attr_anchors = nn.Parameter(
            torch.randn(n_relational, embed_dim) * init_std
        )

        # ATPrompt启发：属性连接器（学习属性间关系）
        self.attr_connector_1 = nn.Parameter(torch.randn(1, embed_dim) * init_std)  # spatial-semantic连接
        self.attr_connector_2 = nn.Parameter(torch.randn(1, embed_dim) * init_std)  # semantic-relational连接

        self.total_anchors = n_spatial + n_semantic + n_relational + 2  # +2 for connectors
        
    def get_anchor_embeddings(self):
        """返回按ATPrompt启发的分层结构排列的所有anchor embeddings"""
        # ATPrompt结构: [spatial] + [connector_1] + [semantic] + [connector_2] + [relational]
        return torch.cat([
            self.spatial_attr_anchors,      # 空间属性锚点
            self.attr_connector_1,          # 空间-语义连接器
            self.semantic_attr_anchors,     # 语义属性锚点
            self.attr_connector_2,          # 语义-关系连接器
            self.relational_attr_anchors    # 关系属性锚点
        ], dim=0)

    def get_anchor_description(self):
        """返回属性感知anchors的描述，用于调试"""
        return {
            'spatial_attr': f"{self.n_spatial} anchors for spatial attributes (position, size, orientation)",
            'semantic_attr': f"{self.n_semantic} anchors for semantic attributes (object class, function)",
            'relational_attr': f"{self.n_relational} anchors for relational attributes (containment, adjacency, comparison)",
            'connectors': "2 attribute connectors for learning inter-attribute relationships",
            'total_structure': "[spatial] + [conn1] + [semantic] + [conn2] + [relational]"
        }

    def initialize_with_question_prior(self, question_text, word_embeddings_fn=None):
        """基于问题内容初始化属性锚点（ATPrompt启发的智能初始化）"""
        if word_embeddings_fn is None:
            return  # 如果没有词嵌入函数，使用随机初始化

        # 提取问题中的属性线索
        spatial_keywords = self.extract_spatial_keywords(question_text)
        semantic_keywords = self.extract_semantic_keywords(question_text)
        relational_keywords = self.extract_relational_keywords(question_text)

        # 使用词嵌入初始化（如果有相关关键词）
        if spatial_keywords:
            spatial_emb = word_embeddings_fn(spatial_keywords[0])
            if spatial_emb is not None:
                self.spatial_attr_anchors.data[0] = spatial_emb

        if semantic_keywords:
            semantic_emb = word_embeddings_fn(semantic_keywords[0])
            if semantic_emb is not None:
                self.semantic_attr_anchors.data[0] = semantic_emb

        if relational_keywords:
            relational_emb = word_embeddings_fn(relational_keywords[0])
            if relational_emb is not None:
                self.relational_attr_anchors.data[0] = relational_emb

    def extract_spatial_keywords(self, text):
        """提取空间相关关键词"""
        spatial_words = ['where', 'position', 'location', 'left', 'right', 'top', 'bottom', 'center', 'corner']
        return [word for word in spatial_words if word in text.lower()]

    def extract_semantic_keywords(self, text):
        """提取语义相关关键词"""
        # 从问题中提取选项词汇作为语义关键词
        import re
        match = re.search(r'(?:a\s+|an\s+|the\s+)?(\w+)\s+or\s+(?:a\s+|an\s+|the\s+)?(\w+)', text, re.IGNORECASE)
        if match:
            return [match.group(1).strip(), match.group(2).strip()]
        return []

    def extract_relational_keywords(self, text):
        """提取关系相关关键词"""
        relational_words = ['or', 'versus', 'between', 'compare', 'contain', 'inside', 'outside', 'near', 'far']
        return [word for word in relational_words if word in text.lower()]


# 注意：create_anchored_embeddings函数已被新的token-based方法替代
# 新方法直接修改embedding层的权重，更加兼容


def update_attention_mask(attention_mask, n_anchors):
    """更新attention mask以包含新增的anchor tokens"""
    batch_size, seq_len = attention_mask.shape
    
    # 为anchor tokens添加attention mask (全为1，表示attend to these tokens)
    anchor_mask = torch.ones(batch_size, n_anchors, dtype=attention_mask.dtype, device=attention_mask.device)
    
    # 连接anchor mask和原始mask
    new_attention_mask = torch.cat([anchor_mask, attention_mask], dim=1)
    
    return new_attention_mask


def extract_options_from_question(question_text):
    """提取问题中的选项，用于关系分析"""
    import re
    # 改进的正则表达式，处理 "a bottle or a banana" 这样的情况
    # 匹配 "a/an/the [object1] or a/an/the [object2]" 模式
    match = re.search(r'(?:a\s+|an\s+|the\s+)?(\w+)\s+or\s+(?:a\s+|an\s+|the\s+)?(\w+)', question_text, re.IGNORECASE)
    if match:
        return match.group(1).strip(), match.group(2).strip()
    
    # 如果上面的模式不匹配，尝试更简单的模式
    match = re.search(r'(\w+)\s+or\s+(\w+)', question_text, re.IGNORECASE)
    if match:
        obj1, obj2 = match.group(1).strip(), match.group(2).strip()
        # 过滤掉常见的冠词
        if obj1.lower() in ['a', 'an', 'the']:
            return obj2, None
        if obj2.lower() in ['a', 'an', 'the']:
            return obj1, None
        return obj1, obj2
    
    return None, None


def analyze_object_relationship(opt1, opt2):
    """
    简化的对象关系分析，返回通用的关系类型

    Returns:
        relationship_type: 'comparison' for any two objects, 'unrelated' if no objects found
    """
    if not opt1 or not opt2:
        return 'unrelated'

    # 简化为通用的comparison关系，让模型自己学习具体的关系模式
    return 'comparison'


def compute_four_dimensional_reward(attention_loss, generated_text, anchor_attention_weights,
                                   anchor_embeddings, question_options, adaptive_weights=None):
    """
    基于ATPrompt启发的四维混合奖励函数

    Args:
        attention_loss: 原始注意力损失
        generated_text: 生成的文本
        anchor_attention_weights: 锚点注意力权重
        anchor_embeddings: 锚点嵌入（包含spatial, semantic, relational）
        question_options: 问题选项
        adaptive_weights: 自适应权重字典

    Returns:
        total_reward: 四维混合奖励
        reward_breakdown: 各维度奖励分解
    """
    # 默认权重
    if adaptive_weights is None:
        adaptive_weights = {
            'attention': 0.30,
            'quality': 0.30,
            'anchor_effectiveness': 0.20,
            'attribute_coordination': 0.20
        }

    # 1. Attention Loss奖励 (原有)
    attention_reward = -attention_loss.item() if isinstance(attention_loss, torch.Tensor) else -attention_loss
    attention_reward_normalized = torch.sigmoid(torch.tensor(attention_reward / 100.0)).item()

    # 2. 生成质量奖励 (原有)
    quality_reward = compute_quality_score(generated_text, question_options)

    # 3. 锚点有效性奖励 (原有)
    anchor_effectiveness = compute_anchor_effectiveness(anchor_attention_weights)

    # 4. 属性协调性奖励 (新增 - ATPrompt启发)
    attribute_coordination = compute_attribute_coordination_reward(anchor_embeddings)

    # 加权组合
    total_reward = (adaptive_weights['attention'] * attention_reward_normalized +
                   adaptive_weights['quality'] * quality_reward +
                   adaptive_weights['anchor_effectiveness'] * anchor_effectiveness +
                   adaptive_weights['attribute_coordination'] * attribute_coordination)

    reward_breakdown = {
        'attention_reward': attention_reward_normalized,
        'quality_reward': quality_reward,
        'anchor_effectiveness': anchor_effectiveness,
        'attribute_coordination': attribute_coordination,
        'total_reward': total_reward,
        'weights_used': adaptive_weights
    }

    return total_reward, reward_breakdown


def compute_quality_score(generated_text, question_options):
    """计算生成质量评分 (0-1)"""
    if not generated_text or len(generated_text.strip()) == 0:
        return 0.0

    scores = []

    # 1. 重复检测 (检测"P a p a"这类问题)
    repetition_score = 1.0 - detect_abnormal_repetition(generated_text)
    scores.append(repetition_score)

    # 2. 长度合理性
    length_score = compute_reasonable_length(generated_text)
    scores.append(length_score)

    # 3. 词汇多样性
    diversity_score = compute_vocabulary_diversity(generated_text)
    scores.append(diversity_score)

    # 4. 任务相关性 (是否包含选项词汇)
    relevance_score = compute_option_relevance(generated_text, question_options)
    scores.append(relevance_score)

    # 加权平均 (重复检测和任务相关性权重更高)
    weights = [0.3, 0.2, 0.2, 0.3]
    quality_score = sum(w * s for w, s in zip(weights, scores))

    return max(0.0, min(1.0, quality_score))  # 确保在[0,1]范围内


def detect_abnormal_repetition(text):
    """检测异常重复模式"""
    if not text:
        return 0.0

    # 检测字符级重复 (如 "P a p a p a")
    char_repetition = 0.0
    for i in range(len(text) - 2):
        if text[i] == text[i+2] and text[i+1] == ' ':
            char_repetition += 1
    char_repetition_ratio = char_repetition / max(1, len(text))

    # 检测词汇重复
    words = text.split()
    if len(words) <= 1:
        return 0.0

    word_counts = {}
    for word in words:
        word_counts[word] = word_counts.get(word, 0) + 1

    max_word_count = max(word_counts.values())
    word_repetition_ratio = max_word_count / len(words)

    # 检测标点符号重复 (如 ";;;;;;;;;;;;")
    punct_repetition = 0.0
    for char in text:
        if char in '.,;:!?':
            punct_repetition += 1
    punct_repetition_ratio = punct_repetition / max(1, len(text))

    # 综合重复评分
    total_repetition = max(char_repetition_ratio, word_repetition_ratio, punct_repetition_ratio)
    return min(1.0, total_repetition * 2)  # 放大惩罚


def compute_reasonable_length(text):
    """计算长度合理性评分"""
    if not text:
        return 0.0

    words = text.split()
    word_count = len(words)

    # 理想长度范围: 5-50个词
    if 5 <= word_count <= 50:
        return 1.0
    elif word_count < 5:
        return word_count / 5.0  # 太短的惩罚
    else:
        return max(0.1, 1.0 - (word_count - 50) / 100.0)  # 太长的惩罚


def compute_vocabulary_diversity(text):
    """计算词汇多样性"""
    if not text:
        return 0.0

    words = text.split()
    if len(words) <= 1:
        return 1.0 if len(words) == 1 else 0.0

    unique_words = set(words)
    diversity = len(unique_words) / len(words)
    return diversity


def compute_option_relevance(text, question_options):
    """计算与问题选项的相关性"""
    if not text or not question_options:
        return 0.5  # 中性评分

    text_lower = text.lower()
    relevance_score = 0.0

    # 检查是否包含选项词汇
    for option in question_options:
        if option and option.lower() in text_lower:
            relevance_score += 0.5

    return min(1.0, relevance_score)


def compute_anchor_effectiveness(anchor_attention_weights):
    """计算锚点有效性 (解决0.000000问题)"""
    if anchor_attention_weights is None:
        return 0.0

    if isinstance(anchor_attention_weights, torch.Tensor):
        mean_attention = anchor_attention_weights.mean().item()
    else:
        mean_attention = float(anchor_attention_weights)

    # 将注意力权重映射到0-1范围
    effectiveness = min(1.0, max(0.0, mean_attention * 1000))  # 放大小数值
    return effectiveness


def compute_attribute_coordination_reward(anchor_embeddings):
    """
    计算属性协调性奖励 (ATPrompt启发的新维度)
    确保空间-语义-关系属性之间的协调一致性
    """
    if not isinstance(anchor_embeddings, torch.Tensor):
        return 0.5  # 默认中性评分

    # 假设anchor_embeddings的结构: [spatial, conn1, semantic, conn2, relational]
    if anchor_embeddings.shape[0] < 5:
        return 0.5

    try:
        # 提取不同属性的嵌入
        spatial_emb = anchor_embeddings[0:2]  # 前2个是spatial
        semantic_emb = anchor_embeddings[3:5] if anchor_embeddings.shape[0] >= 5 else anchor_embeddings[2:3]  # semantic
        relational_emb = anchor_embeddings[-2:] if anchor_embeddings.shape[0] >= 6 else anchor_embeddings[-1:]  # relational

        # 计算属性间的余弦相似度
        spatial_mean = spatial_emb.mean(dim=0)
        semantic_mean = semantic_emb.mean(dim=0)
        relational_mean = relational_emb.mean(dim=0)

        # 空间-语义一致性
        spatial_semantic_sim = torch.cosine_similarity(spatial_mean, semantic_mean, dim=0)

        # 语义-关系一致性
        semantic_relational_sim = torch.cosine_similarity(semantic_mean, relational_mean, dim=0)

        # 综合协调性评分
        coordination_score = (spatial_semantic_sim + semantic_relational_sim) / 2.0

        # 映射到0-1范围 (余弦相似度范围是-1到1)
        coordination_reward = (coordination_score + 1.0) / 2.0

        return max(0.0, min(1.0, coordination_reward.item()))

    except Exception as e:
        print(f"Warning: Error computing attribute coordination: {e}")
        return 0.5  # 出错时返回中性评分


def adaptive_attribute_weights(question_text, step=0, total_steps=30):
    """
    基于ATPrompt启发的自适应权重机制
    根据问题类型和优化阶段动态调整四维奖励权重
    """
    # 基础权重（问题类型感知）
    base_weights = get_question_type_weights(question_text)

    # 阶段性权重调整（优化过程中的动态调整）
    stage_weights = get_stage_adaptive_weights(step, total_steps)

    # 组合权重
    final_weights = {}
    for key in base_weights:
        final_weights[key] = 0.7 * base_weights[key] + 0.3 * stage_weights[key]

    # 确保权重和为1
    total_weight = sum(final_weights.values())
    for key in final_weights:
        final_weights[key] /= total_weight

    return final_weights


def get_question_type_weights(question_text):
    """根据问题类型确定基础权重"""
    question_lower = question_text.lower()

    # 空间问题：空间属性权重更高
    if any(word in question_lower for word in ['where', 'position', 'location', 'left', 'right', 'top', 'bottom']):
        return {
            'attention': 0.25,
            'quality': 0.25,
            'anchor_effectiveness': 0.35,  # 空间问题更关注锚点有效性
            'attribute_coordination': 0.15
        }

    # 识别问题：语义属性权重更高
    elif any(word in question_lower for word in ['what', 'which', 'identify']):
        return {
            'attention': 0.25,
            'quality': 0.40,  # 识别问题更关注生成质量
            'anchor_effectiveness': 0.20,
            'attribute_coordination': 0.15
        }

    # 比较问题：关系属性权重更高
    elif 'or' in question_lower or any(word in question_lower for word in ['versus', 'between', 'compare']):
        return {
            'attention': 0.35,  # 比较问题更关注attention
            'quality': 0.25,
            'anchor_effectiveness': 0.15,
            'attribute_coordination': 0.25  # 关系问题更关注属性协调
        }

    # 默认平衡权重
    else:
        return {
            'attention': 0.30,
            'quality': 0.30,
            'anchor_effectiveness': 0.20,
            'attribute_coordination': 0.20
        }


def get_stage_adaptive_weights(step, total_steps):
    """根据优化阶段调整权重"""
    progress = step / max(1, total_steps)

    # 初期：更关注锚点有效性，确保锚点能够关注到图像
    if progress < 0.3:
        return {
            'attention': 0.25,
            'quality': 0.20,
            'anchor_effectiveness': 0.40,  # 初期高权重
            'attribute_coordination': 0.15
        }

    # 中期：平衡attention loss和质量
    elif progress < 0.7:
        return {
            'attention': 0.35,
            'quality': 0.30,
            'anchor_effectiveness': 0.20,
            'attribute_coordination': 0.15
        }

    # 后期：更关注生成质量的精细调优
    else:
        return {
            'attention': 0.25,
            'quality': 0.40,  # 后期高权重
            'anchor_effectiveness': 0.15,
            'attribute_coordination': 0.20
        }


def update_weights_based_on_performance(current_weights, performance_history):
    """根据性能历史动态更新权重"""
    if len(performance_history) < 3:
        return current_weights

    # 分析最近3步的各维度表现
    recent_performance = performance_history[-3:]

    # 计算各维度的平均表现
    avg_performance = {}
    for dimension in ['attention_reward', 'quality_reward', 'anchor_effectiveness', 'attribute_coordination']:
        scores = [perf.get(dimension, 0.5) for perf in recent_performance]
        avg_performance[dimension] = sum(scores) / len(scores)

    # 找出表现最差的维度
    worst_dimension = min(avg_performance, key=avg_performance.get)

    # 调整权重：给表现差的维度更多关注
    adjusted_weights = current_weights.copy()

    # 映射维度名称到权重键
    dimension_to_weight = {
        'attention_reward': 'attention',
        'quality_reward': 'quality',
        'anchor_effectiveness': 'anchor_effectiveness',
        'attribute_coordination': 'attribute_coordination'
    }

    worst_weight_key = dimension_to_weight[worst_dimension]

    # 增加表现差的维度的权重（最多增加0.1）
    boost = min(0.1, (0.5 - avg_performance[worst_dimension]) * 0.2)
    adjusted_weights[worst_weight_key] += boost

    # 从其他维度按比例减少权重
    other_keys = [k for k in adjusted_weights.keys() if k != worst_weight_key]
    reduction_per_key = boost / len(other_keys)
    for key in other_keys:
        adjusted_weights[key] = max(0.05, adjusted_weights[key] - reduction_per_key)

    # 重新归一化
    total_weight = sum(adjusted_weights.values())
    for key in adjusted_weights:
        adjusted_weights[key] /= total_weight

    return adjusted_weights


def compute_four_dimensional_reward(attention_loss, generated_text, anchor_attention_weights,
                                   anchor_embeddings, question_options, adaptive_weights=None):
    """
    基于ATPrompt启发的四维混合奖励函数

    Args:
        attention_loss: 原始注意力损失
        generated_text: 生成的文本
        anchor_attention_weights: 锚点注意力权重
        anchor_embeddings: 锚点嵌入（包含spatial, semantic, relational）
        question_options: 问题选项
        adaptive_weights: 自适应权重字典

    Returns:
        total_reward: 四维混合奖励
        reward_breakdown: 各维度奖励分解
    """
    # 默认权重
    if adaptive_weights is None:
        adaptive_weights = {
            'attention': 0.30,
            'quality': 0.30,
            'anchor_effectiveness': 0.20,
            'attribute_coordination': 0.20
        }

    # 1. Attention Loss奖励 (原有)
    attention_reward = -attention_loss.item() if isinstance(attention_loss, torch.Tensor) else -attention_loss
    attention_reward_normalized = torch.sigmoid(torch.tensor(attention_reward / 100.0)).item()

    # 2. 生成质量奖励 (原有)
    quality_reward = compute_quality_score(generated_text, question_options)

    # 3. 锚点有效性奖励 (原有)
    anchor_effectiveness = compute_anchor_effectiveness(anchor_attention_weights)

    # 4. 属性协调性奖励 (新增 - ATPrompt启发)
    attribute_coordination = compute_attribute_coordination_reward(anchor_embeddings)

    # 加权组合
    total_reward = (adaptive_weights['attention'] * attention_reward_normalized +
                   adaptive_weights['quality'] * quality_reward +
                   adaptive_weights['anchor_effectiveness'] * anchor_effectiveness +
                   adaptive_weights['attribute_coordination'] * attribute_coordination)

    reward_breakdown = {
        'attention_reward': attention_reward_normalized,
        'quality_reward': quality_reward,
        'anchor_effectiveness': anchor_effectiveness,
        'attribute_coordination': attribute_coordination,
        'total_reward': total_reward,
        'weights_used': adaptive_weights
    }

    return total_reward, reward_breakdown


def compute_quality_score(generated_text, question_options):
    """计算生成质量评分 (0-1)"""
    if not generated_text or len(generated_text.strip()) == 0:
        return 0.0

    scores = []

    # 1. 重复检测 (检测"P a p a"这类问题)
    repetition_score = 1.0 - detect_abnormal_repetition(generated_text)
    scores.append(repetition_score)

    # 2. 长度合理性
    length_score = compute_reasonable_length(generated_text)
    scores.append(length_score)

    # 3. 词汇多样性
    diversity_score = compute_vocabulary_diversity(generated_text)
    scores.append(diversity_score)

    # 4. 任务相关性 (是否包含选项词汇)
    relevance_score = compute_option_relevance(generated_text, question_options)
    scores.append(relevance_score)

    # 加权平均 (重复检测和任务相关性权重更高)
    weights = [0.3, 0.2, 0.2, 0.3]
    quality_score = sum(w * s for w, s in zip(weights, scores))

    return max(0.0, min(1.0, quality_score))  # 确保在[0,1]范围内


def detect_abnormal_repetition(text):
    """检测异常重复模式"""
    if not text:
        return 0.0

    # 检测字符级重复 (如 "P a p a p a")
    char_repetition = 0.0
    for i in range(len(text) - 2):
        if text[i] == text[i+2] and text[i+1] == ' ':
            char_repetition += 1
    char_repetition_ratio = char_repetition / max(1, len(text))

    # 检测词汇重复
    words = text.split()
    if len(words) <= 1:
        return 0.0

    word_counts = {}
    for word in words:
        word_counts[word] = word_counts.get(word, 0) + 1

    max_word_count = max(word_counts.values())
    word_repetition_ratio = max_word_count / len(words)

    # 检测标点符号重复 (如 ";;;;;;;;;;;;")
    punct_repetition = 0.0
    for char in text:
        if char in '.,;:!?':
            punct_repetition += 1
    punct_repetition_ratio = punct_repetition / max(1, len(text))

    # 综合重复评分
    total_repetition = max(char_repetition_ratio, word_repetition_ratio, punct_repetition_ratio)
    return min(1.0, total_repetition * 2)  # 放大惩罚


def compute_reasonable_length(text):
    """计算长度合理性评分"""
    if not text:
        return 0.0

    words = text.split()
    word_count = len(words)

    # 理想长度范围: 5-50个词
    if 5 <= word_count <= 50:
        return 1.0
    elif word_count < 5:
        return word_count / 5.0  # 太短的惩罚
    else:
        return max(0.1, 1.0 - (word_count - 50) / 100.0)  # 太长的惩罚


def compute_vocabulary_diversity(text):
    """计算词汇多样性"""
    if not text:
        return 0.0

    words = text.split()
    if len(words) <= 1:
        return 1.0 if len(words) == 1 else 0.0

    unique_words = set(words)
    diversity = len(unique_words) / len(words)
    return diversity


def compute_option_relevance(text, question_options):
    """计算与问题选项的相关性"""
    if not text or not question_options:
        return 0.5  # 中性评分

    text_lower = text.lower()
    relevance_score = 0.0

    # 检查是否包含选项词汇
    for option in question_options:
        if option and option.lower() in text_lower:
            relevance_score += 0.5

    return min(1.0, relevance_score)


def compute_anchor_effectiveness(anchor_attention_weights):
    """计算锚点有效性 (解决0.000000问题)"""
    if anchor_attention_weights is None:
        return 0.0

    if isinstance(anchor_attention_weights, torch.Tensor):
        mean_attention = anchor_attention_weights.mean().item()
    else:
        mean_attention = float(anchor_attention_weights)

    # 将注意力权重映射到0-1范围
    effectiveness = min(1.0, max(0.0, mean_attention * 1000))  # 放大小数值
    return effectiveness


def compute_attribute_coordination_reward(anchor_embeddings):
    """
    计算属性协调性奖励 (ATPrompt启发的新维度)
    确保空间-语义-关系属性之间的协调一致性
    """
    if not isinstance(anchor_embeddings, torch.Tensor):
        return 0.5  # 默认中性评分

    # 假设anchor_embeddings的结构: [spatial, conn1, semantic, conn2, relational]
    if anchor_embeddings.shape[0] < 5:
        return 0.5

    try:
        # 提取不同属性的嵌入
        spatial_emb = anchor_embeddings[0:2]  # 前2个是spatial
        semantic_emb = anchor_embeddings[3:5] if anchor_embeddings.shape[0] >= 5 else anchor_embeddings[2:3]  # semantic
        relational_emb = anchor_embeddings[-2:] if anchor_embeddings.shape[0] >= 6 else anchor_embeddings[-1:]  # relational

        # 计算属性间的余弦相似度
        spatial_mean = spatial_emb.mean(dim=0)
        semantic_mean = semantic_emb.mean(dim=0)
        relational_mean = relational_emb.mean(dim=0)

        # 空间-语义一致性
        spatial_semantic_sim = torch.cosine_similarity(spatial_mean, semantic_mean, dim=0)

        # 语义-关系一致性
        semantic_relational_sim = torch.cosine_similarity(semantic_mean, relational_mean, dim=0)

        # 综合协调性评分
        coordination_score = (spatial_semantic_sim + semantic_relational_sim) / 2.0

        # 映射到0-1范围 (余弦相似度范围是-1到1)
        coordination_reward = (coordination_score + 1.0) / 2.0

        return max(0.0, min(1.0, coordination_reward.item()))

    except Exception as e:
        print(f"Warning: Error computing attribute coordination: {e}")
        return 0.5  # 出错时返回中性评分


def optimize_visual_anchors_for_question(question, model, processor, device, args):
    """
    为单个问题优化visual anchor embeddings
    """
    # 准备图像和问题
    image_path = os.path.join(args.data_path, 'image', 
                             question['image_path'].split('/')[-2], 
                             question['image_path'].split('/')[-1])
    image = Image.open(image_path)
    iw, ih = image.size
    
    original_question = question['text'].replace('<location> ', '')
    prompt = f"USER: <image>\n{original_question} ASSISTANT:"
    
    if args.verbose:
        print(f"Original question: {original_question}")
    
    # 分析问题中的对象关系
    opt1, opt2 = extract_options_from_question(original_question)
    relationship_type = analyze_object_relationship(opt1, opt2)
    
    if args.verbose:
        print(f"Objects: {opt1} vs {opt2}, Relationship: {relationship_type}")
    
    # 处理输入
    inputs = processor(text=prompt, images=image, return_tensors="pt").to(device)
    
    # 获取图像token位置
    img_token_positions = torch.where(inputs['input_ids'] == 32000)
    if len(img_token_positions[0]) > 0:
        img_token_idx = img_token_positions[1][0].item()
    else:
        raise ValueError("No image token found")
    
    # 创建visual mask
    mask = create_visual_mask(question, image, ih, iw, args)
    mask = mask.to(device)
    
    # 记录原始inputs用于后续处理
    original_input_ids = inputs.input_ids.clone()
    original_attention_mask = inputs.attention_mask.clone()
    
    # 获取text embeddings（不处理图像特征，让模型内部处理）
    with torch.no_grad():
        original_inputs_embeds = model.get_input_embeddings()(inputs.input_ids)
    
    # 获取模型的数据类型（适配量化模型）
    model_dtype = original_inputs_embeds.dtype
    print(f"Model embeddings dtype: {model_dtype}")
    
    # 初始化Attribute-Aware Visual Anchor Optimizer - 基于ATPrompt思想
    anchor_optimizer = AttributeAwareVisualAnchorOptimizer(
        embed_dim=original_inputs_embeds.shape[-1],
        n_spatial=args.n_spatial_anchors,
        n_semantic=args.n_semantic_anchors,
        n_relational=args.n_relational_anchors,
        init_std=args.anchor_init_std
    ).to(device)
    
    # 改进初始化：使用小的随机值而非zeros，参考原版visual_prompt的初始化方式
    with torch.no_grad():
        for param in anchor_optimizer.parameters():
            # 使用与原版visual_prompt类似的初始化：基于现有embeddings的小扰动
            param.data = torch.randn_like(param.data, dtype=model_dtype) * args.anchor_init_std
            param.requires_grad_(True)
    
    # 不再使用Adam优化器，改用手动梯度下降（学习llava_roc.py）
    # optimizer = torch.optim.Adam(anchor_optimizer.parameters(), lr=args.lr)
    
    # 初始化EMA历史状态
    anchor_ema_state = copy.deepcopy(anchor_optimizer.state_dict())
    
    if args.verbose:
        print(f"Anchor configuration: {anchor_optimizer.get_anchor_description()}")
        print(f"Total trainable parameters: {sum(p.numel() for p in anchor_optimizer.parameters() if p.requires_grad)}")
        print(f"Using manual gradient descent (lr={args.lr}) like llava_roc.py")
    
    # 检查token ids范围安全性
    vocab_size = model.get_input_embeddings().weight.shape[0]
    anchor_start_id = 32001
    anchor_end_id = anchor_start_id + anchor_optimizer.total_anchors
    
    if anchor_end_id > vocab_size:
        print(f"Warning: Anchor token ids ({anchor_start_id}-{anchor_end_id}) exceed vocab size ({vocab_size})")
        print("Adjusting to use safe token range...")
        anchor_start_id = vocab_size - anchor_optimizer.total_anchors - 1
    
    if args.verbose:
        print(f"Using anchor token ids: {anchor_start_id} to {anchor_start_id + anchor_optimizer.total_anchors - 1}")
    
    # 记录原始输出用于对比
    with torch.no_grad():
        get_local.clear()
        original_outputs = model.generate(
            **inputs,
            max_new_tokens=30,
            return_dict_in_generate=True,
            output_attentions=True,
            pad_token_id=processor.tokenizer.eos_token_id
        )
        original_generated_text = processor.batch_decode(original_outputs.sequences, skip_special_tokens=True)[0]
    
    print(f"Original output: {original_generated_text}")
    
    # 计算初始loss用于对比
    initial_loss = None
    with torch.no_grad():
        # 获取初始attention maps来计算baseline loss
        get_local.clear()
        initial_model_outputs = model(
            **inputs,
            output_attentions=True,
            return_dict=True
        )
        
        # 尝试获取attention maps
        attention_keys = [
            'force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 
            'eager_attention_forward'
        ]
        
        ori_attention_maps = None
        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                break
        
        if ori_attention_maps is None and hasattr(initial_model_outputs, 'attentions') and initial_model_outputs.attentions is not None:
            ori_attention_maps = initial_model_outputs.attentions
        
        if ori_attention_maps is not None:
            attention_maps = [att for att in ori_attention_maps if att is not None and att.shape[-2] > 1]
            if len(attention_maps) > 0:
                mean_att = torch.cat([att.to(device) for att in attention_maps], 0).mean(0)
                target2img_rel = mean_att[:, img_token_idx + args.H * args.W:, 
                                        img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0).unsqueeze(0)
                initial_loss = args.alpha * compute_ca_loss(target2img_rel.to(mask.device), 
                                                          masks=[mask], 
                                                          choice=args.visual_prompt, 
                                                          object_positions=None)
                initial_loss = initial_loss.item()
        
        get_local.clear()
    
    if initial_loss is not None:
        print(f"Initial loss: {initial_loss:.4f}")
    
    # 优化循环 - 集成四维奖励函数和自适应权重
    loss_history = []
    reward_history = []  # 新增：奖励历史
    performance_history = []  # 新增：性能历史
    best_loss = float('inf')
    best_reward = float('-inf')  # 新增：最佳奖励
    best_anchor_state = None

    # 提取问题选项用于质量评估
    question_options = [opt1, opt2] if opt1 and opt2 else []

    for step in range(args.T):
        # 自适应权重调整（ATPrompt启发）
        if step == 0:
            adaptive_weights = adaptive_attribute_weights(original_question, step, args.T)
        else:
            # 基于性能历史动态调整权重
            base_weights = adaptive_attribute_weights(original_question, step, args.T)
            adaptive_weights = update_weights_based_on_performance(base_weights, performance_history)

        if args.verbose and step < 3:
            print(f"Step {step} adaptive weights: {adaptive_weights}")
        # 创建包含anchor tokens的新input_ids
        anchor_token_ids = torch.arange(
            anchor_start_id, anchor_start_id + anchor_optimizer.total_anchors,
            device=device, dtype=torch.long
        ).unsqueeze(0)  # [1, n_anchors]
        
        # 在开头插入anchor token ids
        new_input_ids = torch.cat([
            anchor_token_ids,
            original_input_ids
        ], dim=1)
        
        # 更新attention mask
        new_attention_mask = update_attention_mask(original_attention_mask, anchor_optimizer.total_anchors)
        
        # 更新image token索引 (因为添加了anchor tokens)
        updated_img_token_idx = img_token_idx + anchor_optimizer.total_anchors
        
        # 方法改进：手动构建包含anchor embeddings的inputs_embeds
        # 获取anchor embeddings (保持梯度)
        anchor_embeddings = anchor_optimizer.get_anchor_embeddings().unsqueeze(0)  # [1, n_anchors, embed_dim]
        
        # 获取原始文本的embeddings
        with torch.no_grad():
            original_text_embeds = model.get_input_embeddings()(original_input_ids)
        
        # 确保数据类型一致
        anchor_embeddings = anchor_embeddings.to(original_text_embeds.dtype)
        
        # 拼接anchor embeddings和原始embeddings (保持anchor部分的梯度)
        full_inputs_embeds = torch.cat([
            anchor_embeddings,  # 有梯度的anchor embeddings
            original_text_embeds  # 原始文本embeddings
        ], dim=1)
        
        # 前向传播 - 使用inputs_embeds而不是input_ids
        get_local.clear()
        
        # 改用model()前向传播而不是generate()，以保持梯度连接
        with torch.enable_grad():
            model_outputs = model(
                inputs_embeds=full_inputs_embeds,
                pixel_values=inputs.pixel_values,
                attention_mask=new_attention_mask,
                output_attentions=True,
                return_dict=True
            )
        
        # 生成一些tokens来触发注意力计算 (可选，主要用于获取attention patterns)
        # 注意：这里我们主要关心attention maps，不一定需要完整的生成
        
        # 获取注意力图
        attention_keys = [
            'force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 
            'eager_attention_forward'
        ]
        
        ori_attention_maps = None
        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                break
        
        if ori_attention_maps is None:
            print(f"Warning: No attention maps found at step {step}")
            # 如果没有找到attention maps，尝试使用model outputs中的attentions
            if hasattr(model_outputs, 'attentions') and model_outputs.attentions is not None:
                ori_attention_maps = model_outputs.attentions
            else:
                continue
            
        # 处理注意力图
        attention_maps = [att for att in ori_attention_maps if att is not None and att.shape[-2] > 1]

        if len(attention_maps) > 0:
            mean_att = torch.cat([att.to(device) for att in attention_maps], 0).mean(0)

            # 验证attention map维度和索引计算
            att_seq_len = mean_att.shape[-1]  # attention map的序列长度
            expected_seq_len = anchor_optimizer.total_anchors + original_input_ids.shape[1]  # 期望的序列长度

            if args.verbose and step == 0:
                print(f"Attention map shape: {mean_att.shape}")
                print(f"Expected sequence length: {expected_seq_len}, Actual: {att_seq_len}")
                print(f"Original img_token_idx: {img_token_idx}, Updated: {updated_img_token_idx}")
                print(f"Image tokens range: {updated_img_token_idx} to {updated_img_token_idx + args.H * args.W}")
                print(f"Target tokens start from: {updated_img_token_idx + args.H * args.W}")

            # 检查索引范围的有效性
            img_end_idx = updated_img_token_idx + args.H * args.W
            target_start_idx = img_end_idx

            if img_end_idx > att_seq_len or target_start_idx >= att_seq_len:
                print(f"Warning: Index out of range at step {step}. Attention seq_len: {att_seq_len}, img_end: {img_end_idx}, target_start: {target_start_idx}")
                continue

            # 计算target到image的注意力 (注意索引偏移)
            target2img_rel = mean_att[:, target_start_idx:,
                                    updated_img_token_idx:img_end_idx].mean(axis=0).mean(axis=0).unsqueeze(0)

            # 检查数值稳定性
            if torch.isnan(target2img_rel).any() or torch.isinf(target2img_rel).any():
                print(f"Warning: NaN/Inf detected in attention maps at step {step}")
                continue
            
            # 计算传统的attention loss
            attention_loss = args.alpha * compute_ca_loss(target2img_rel.to(mask.device),
                                                        masks=[mask],
                                                        choice=args.visual_prompt,
                                                        object_positions=None)

            # 检查loss数值稳定性
            if torch.isnan(attention_loss) or torch.isinf(attention_loss):
                print(f"Warning: NaN/Inf attention loss detected at step {step}, skipping")
                continue

            # 生成文本用于质量评估
            with torch.no_grad():
                temp_outputs = model.generate(
                    inputs_embeds=full_inputs_embeds,
                    pixel_values=inputs.pixel_values,
                    attention_mask=new_attention_mask,
                    max_new_tokens=30,
                    pad_token_id=processor.tokenizer.eos_token_id
                )
                temp_generated_text = processor.batch_decode(temp_outputs, skip_special_tokens=True)[0]

            # 计算锚点注意力权重
            anchor2img_attention = mean_att[:, 0:anchor_optimizer.total_anchors,
                                           updated_img_token_idx:updated_img_token_idx + args.H * args.W]

            # 计算四维混合奖励（ATPrompt启发的核心创新）
            total_reward, reward_breakdown = compute_four_dimensional_reward(
                attention_loss=attention_loss,
                generated_text=temp_generated_text,
                anchor_attention_weights=anchor2img_attention,
                anchor_embeddings=anchor_optimizer.get_anchor_embeddings(),
                question_options=question_options,
                adaptive_weights=adaptive_weights
            )

            # 记录历史
            loss_history.append(attention_loss.item())
            reward_history.append(total_reward)
            performance_history.append(reward_breakdown)

            # 保存最佳结果（基于奖励而非loss）
            if total_reward > best_reward:
                best_reward = total_reward
                best_loss = attention_loss.item()  # 同时更新最佳loss
                best_anchor_state = copy.deepcopy(anchor_optimizer.state_dict())

            if args.verbose:
                print(f"Step {step}: Attention Loss = {attention_loss.item():.4f}, Total Reward = {total_reward:.4f}")
                if step < 3:  # 详细输出前几步的分解
                    print(f"  Reward breakdown: {reward_breakdown}")

            # 使用attention loss进行梯度计算（保持原有的稳定性）
            # 但使用四维奖励进行最佳状态选择

            # 使用torch.autograd.grad计算梯度（参考llava_roc.py的稳定方式）
            anchor_params = list(anchor_optimizer.parameters())
            gradients = torch.autograd.grad(
                attention_loss.requires_grad_(True),
                anchor_params,
                retain_graph=False,
                create_graph=False
            )

            # 诊断：分析梯度强度和attention权重（验证我们的假设）
            if args.verbose and step == 0:
                # 分析anchor tokens与图像tokens之间的attention
                anchor2img_attention = mean_att[:, 0:anchor_optimizer.total_anchors,
                                               updated_img_token_idx:updated_img_token_idx + args.H * args.W]
                print(f"🔍 Diagnostic Analysis:")
                print(f"  Anchor to image attention mean: {anchor2img_attention.mean().item():.6f}")
                print(f"  Anchor to image attention max: {anchor2img_attention.max().item():.6f}")

                # 分析梯度强度
                grad_norms = [grad.norm().item() if grad is not None else 0.0 for grad in gradients]
                print(f"  Anchor gradient norms: {[f'{norm:.6f}' for norm in grad_norms]}")
                print(f"  Total gradient norm: {sum(grad_norms):.6f}")

                # 对比：分析文本到图像的attention作为基准
                text2img_attention = mean_att[:, updated_img_token_idx + args.H * args.W:,
                                             updated_img_token_idx:updated_img_token_idx + args.H * args.W]
                print(f"  Text to image attention mean: {text2img_attention.mean().item():.6f}")
                print(f"  Text to image attention max: {text2img_attention.max().item():.6f}")

            # 梯度裁剪防止梯度爆炸
            total_norm = 0
            for grad in gradients:
                if grad is not None:
                    total_norm += grad.data.norm(2).item() ** 2
            total_norm = total_norm ** 0.5

            if total_norm > args.grad_clip:  # 使用可配置的梯度裁剪阈值
                clip_coef = args.grad_clip / total_norm
                gradients = [grad * clip_coef if grad is not None else None for grad in gradients]

            # 手动梯度下降更新参数
            with torch.no_grad():
                for param, grad in zip(anchor_params, gradients):
                    if grad is not None:
                        # 数值稳定性检查
                        if torch.isnan(grad).any() or torch.isinf(grad).any():
                            print(f"Warning: NaN/Inf gradient detected, skipping update")
                            continue
                        param.data -= args.lr * grad
                        # 确保参数保持正确的数据类型和数值范围
                        param.data = param.data.to(model_dtype)
                        # 限制参数范围防止数值溢出
                        param.data = torch.clamp(param.data, -10.0, 10.0)
            
            # EMA更新 (按llava_roc.py的方式)
            current_state = anchor_optimizer.state_dict()
            for key in anchor_ema_state:
                anchor_ema_state[key] = args.beta * current_state[key] + (1 - args.beta) * anchor_ema_state[key]
            
            # Early stopping
            if args.early_stop and len(loss_history) >= 3:
                recent_losses = loss_history[-3:]
                if max(recent_losses) - min(recent_losses) < args.loss_threshold:
                    print(f"Early stopping at step {step}: loss converged")
                    break
        
        get_local.clear()
        torch.cuda.empty_cache()
    
    # 使用最佳anchor state生成最终输出
    if best_anchor_state is not None:
        anchor_optimizer.load_state_dict(best_anchor_state)

    # 应用EMA状态 (学习llava_roc.py的方式)
    if args.use_ema:
        anchor_optimizer.load_state_dict(anchor_ema_state)

    with torch.no_grad():
        get_local.clear()

        # 创建最终的anchor token ids (虽然不直接使用，但保持一致性)
        final_anchor_token_ids = torch.arange(
            anchor_start_id, anchor_start_id + anchor_optimizer.total_anchors,
            device=device, dtype=torch.long
        ).unsqueeze(0)

        final_input_ids = torch.cat([
            final_anchor_token_ids,
            original_input_ids
        ], dim=1)

        final_attention_mask = update_attention_mask(original_attention_mask, anchor_optimizer.total_anchors)

        # 方法改进：手动构建包含anchor embeddings的inputs_embeds
        # 获取anchor embeddings (保持梯度)
        final_anchor_embeddings = anchor_optimizer.get_anchor_embeddings().unsqueeze(0) # [1, n_anchors, embed_dim]

        # 获取原始文本的embeddings
        with torch.no_grad():
            final_text_embeds = model.get_input_embeddings()(original_input_ids)

        # 确保数据类型一致
        final_anchor_embeddings = final_anchor_embeddings.to(final_text_embeds.dtype)

        # 拼接anchor embeddings和原始embeddings (保持anchor部分的梯度)
        full_final_inputs_embeds = torch.cat([
            final_anchor_embeddings,  # 有梯度的anchor embeddings
            final_text_embeds  # 原始文本embeddings
        ], dim=1)

        # 使用generate()生成最终的可读输出
        final_outputs = model.generate(
            inputs_embeds=full_final_inputs_embeds,
            pixel_values=inputs.pixel_values,
            attention_mask=final_attention_mask,
            max_new_tokens=30,
            return_dict_in_generate=True,
            output_attentions=True,
            pad_token_id=processor.tokenizer.eos_token_id
        )

        optimized_generated_text = processor.batch_decode(final_outputs.sequences, skip_special_tokens=True)[0]
    
    print(f"Optimized output: {optimized_generated_text}")
    print(f"Best loss: {best_loss:.4f}")
    
    # 处理答案格式，确保两个答案都有完整的USER/ASSISTANT格式
    def extract_assistant_response(full_text):
        """提取ASSISTANT后的回答部分"""
        if "ASSISTANT:" in full_text:
            return full_text.split("ASSISTANT:")[-1].strip()
        return full_text.strip()
    
    # 获取用户问题部分（移除<image>标签，符合用户期望格式）
    user_question_part = f"USER: \n{original_question} ASSISTANT:"

    # 提取原始和优化后的助手回答
    original_assistant_response = extract_assistant_response(original_generated_text)
    optimized_assistant_response = extract_assistant_response(optimized_generated_text)

    # 构建完整格式的答案（统一格式：USER: \n问题内容 ASSISTANT: 回答内容）
    formatted_original = f"{user_question_part} {original_assistant_response}"
    formatted_optimized = f"{user_question_part} {optimized_assistant_response}"
    
    return [formatted_original, formatted_optimized], best_loss, relationship_type, initial_loss


def create_visual_mask(question, image, ih, iw, args):
    """创建visual mask (与之前实现相同)"""
    mask = np.zeros((ih, iw))
    
    if args.visual_prompt == 'Box':
        bbox = question['bbox']
        x_min, y_min, x_max, y_max = int(bbox[0]), int(bbox[1]), int(bbox[0] + bbox[2]), int(bbox[1] + bbox[3])
        mask[y_min:y_max, x_min:x_max] = 1
        
    elif args.visual_prompt == 'Mask':
        mask_path = os.path.join(args.data_path, 'mask', question['seg_mask'])
        mask = np.array(Image.open(mask_path))
        
    elif args.visual_prompt == 'Scribble':
        for scri in question['scribble']:
            mask[int(scri[1]), int(scri[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        mask = cv2.normalize(distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
    elif args.visual_prompt == 'Point':
        center_point = question['center_point']
        mask[int(center_point[1]), int(center_point[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        mask = cv2.normalize(distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
    
    # 转换为tensor
    mask = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
        transforms.CenterCrop(args.n_px),
        transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
        transforms.ToTensor(),
    ])(mask)[0]
    
    return mask


def force_eager_attention_with_decorator(model):
    """强制使用eager attention实现并添加装饰器"""
    replaced = 0
    from transformers.models.llama.modeling_llama import LlamaDecoderLayer, eager_attention_forward
    import transformers.models.llama.modeling_llama as llama_module
    
    # 添加get_local装饰器到eager_attention_forward
    @get_local('attn_weights')
    def decorated_eager_attention_forward(module, query, key, value, attention_mask, scaling, dropout=0.0, **kwargs):
        from transformers.models.llama.modeling_llama import repeat_kv
        import torch.nn.functional as F
        
        key_states = repeat_kv(key, module.num_key_value_groups)
        value_states = repeat_kv(value, module.num_key_value_groups)

        attn_weights = torch.matmul(query, key_states.transpose(2, 3)) * scaling
        if attention_mask is not None:
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
            attn_weights = attn_weights + causal_mask

        attn_weights = F.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query.dtype)
        attn_weights = F.dropout(attn_weights, p=dropout, training=module.training)
        attn_output = torch.matmul(attn_weights, value_states)
        attn_output = attn_output.transpose(1, 2).contiguous()

        return attn_output, attn_weights

    # 替换eager_attention_forward函数
    llama_module.eager_attention_forward = decorated_eager_attention_forward
    
    # 设置eager attention
    model.config._attn_implementation = "eager"
    
    for name, module in model.named_modules():
        if isinstance(module, LlamaDecoderLayer):
            module.self_attn.config._attn_implementation = "eager"
            replaced += 1
    
    print(f"Forced {replaced} layers to use eager attention with decorator")
    return model


def main():
    args = parse_args()
    
    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 创建输出目录
    os.makedirs('vis', exist_ok=True) if args.show_att else None
    
    # 加载模型
    print("Loading LLaVA model...")
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
    )
    
    model = LlavaForConditionalGeneration.from_pretrained(
        args.model_path,
        quantization_config=quantization_config,
        device_map="auto"
    )
    
    processor = AutoProcessor.from_pretrained(args.model_path)
    processor.patch_size = 14
    
    # 设置eager attention
    model = force_eager_attention_with_decorator(model)
    
    # 加载问题
    print("Loading questions...")
    questions = [json.loads(q) for q in open(args.question_file, "r")]
    
    # 处理输出文件
    answers_file = args.answers_file
    os.makedirs(os.path.dirname(answers_file), exist_ok=True)
    
    # 检查已存在的结果（用于resume功能）
    answer_ids = []
    if args.resume and os.path.exists(answers_file):
        try:
            with open(answers_file, "r") as f:
                for line in f:
                    if line.strip():
                        answer_data = json.loads(line.strip())
                        answer_ids.append(answer_data['question_id'])
            print(f"Found {len(answer_ids)} existing results, will skip them.")
        except Exception as e:
            print(f"Warning: Error reading existing results file: {e}")
            answer_ids = []
    else:
        # 如果不是resume模式，清空文件
        if not args.resume:
            open(answers_file, 'w').close()
    
    # 统计关系类型
    relationship_stats = {}
    successful_count = 0
    
    # 处理每个问题
    print(f"Processing {len(questions)} questions with Visual Anchor Embeddings...")
    for q in tqdm(questions, desc="Processing Questions"):
        qid = q['id']
        if args.resume and qid in answer_ids:
            print(f"Skipping question {qid} (already processed)")
            continue
            
        print(f"\n{'='*50}")
        print(f"Processing question {qid}: {q['text']}")
        
        try:
            outputs, loss, rel_type, initial_loss = optimize_visual_anchors_for_question(q, model, processor, device, args)
            
            # 更新关系统计
            relationship_stats[rel_type] = relationship_stats.get(rel_type, 0) + 1
            
            # 立即写入单个问题的结果
            result = {
                "question_id": qid,
                "answers": outputs,  # [original, optimized]
                "relevancy": [[0, 0], [0, 0]],  # placeholder
                "label": q['name'],
                "final_loss": float(loss) if isinstance(loss, torch.Tensor) else loss,
                "relationship_type": rel_type,
                "anchor_config": {
                    "spatial": args.n_spatial_anchors,
                    "semantic": args.n_semantic_anchors,
                    "relational": args.n_relational_anchors
                },
                "initial_loss": float(initial_loss) if isinstance(initial_loss, torch.Tensor) else initial_loss
            }
            
            # 每处理完一个问题立即写入文件
            try:
                with open(answers_file, "a", encoding='utf-8') as ans_file:
                    ans_file.write(json.dumps(result, ensure_ascii=False) + '\n')
                    ans_file.flush()
                print(f"✓ Results saved for question {qid}")
                successful_count += 1
            except Exception as write_error:
                print(f"✗ Error writing results for question {qid}: {write_error}")
            
        except Exception as e:
            print(f"✗ Error processing question {qid}: {e}")
            import traceback
            if args.verbose:
                traceback.print_exc()
            continue
    
    # 打印统计信息
    print(f"\n{'='*50}")
    print("Processing Summary:")
    print(f"  Total questions: {len(questions)}")
    print(f"  Successfully processed: {successful_count}")
    print(f"  Skipped (resume): {len(answer_ids) if args.resume else 0}")
    print(f"  Failed: {len(questions) - successful_count - (len(answer_ids) if args.resume else 0)}")
    print(f"\nRelationship Statistics:")
    for rel_type, count in relationship_stats.items():
        print(f"  {rel_type}: {count}")
    
    print(f"\nResults saved to: {answers_file}")
    print("Visual Anchor Embeddings optimization completed!")


if __name__ == "__main__":
    main() 