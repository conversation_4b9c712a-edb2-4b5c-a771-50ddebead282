#!/bin/bash

# Visual Anchor Embeddings Optimization for ControlMLLM
# 基于ATPrompt启发的test-time优化方法

# 设置基本参数 (参考run_verl.sh配置)
MODEL_PATH="/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf"
DATA_PATH="/data1/mrwu_tmp/haoyuan/data/ROC/LVIS"
QUESTION_FILE="/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json"
OUTPUT_DIR="outputs"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=4,5
export TOKENIZERS_PARALLELISM=false

# Visual Prompt类型
VISUAL_PROMPT="Box"  # Box, Mask, Scribble, Point

# 优化参数
T=30                # 优化步数
ALPHA=400          # Loss权重
LR=0.01             # 学习率
GRAD_CLIP=100.0       # 梯度裁剪阈值

# Visual Anchor配置
N_SPATIAL=2        # 空间anchor数量
N_SEMANTIC=2       # 语义anchor数量  
N_RELATIONAL=2     # 关系anchor数量
ANCHOR_INIT_STD=0.01  # anchor初始化标准差

# 高级选项
USE_EMA="--use_ema"              # 使用EMA更新
EARLY_STOP="--early_stop"        # 启用早停
VERBOSE="--verbose"              # 详细输出
# SHOW_ATT="--show_att"          # 显示注意力图(可选)

echo "========================================"
echo "Visual Anchor Embeddings Optimization"
echo "========================================"
echo "Visual Prompt: $VISUAL_PROMPT"
echo "Optimization Steps: $T"
echo "Learning Rate: $LR"
echo "Anchor Config: Spatial=$N_SPATIAL, Semantic=$N_SEMANTIC, Relational=$N_RELATIONAL"
echo "========================================"

# 构造输出文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="${OUTPUT_DIR}/llava_roc_visual_anchors_${VISUAL_PROMPT}_T${T}_lr${LR}_${TIMESTAMP}.json"

# 创建输出目录
mkdir -p $OUTPUT_DIR
mkdir -p vis  # 用于保存注意力图

# 运行优化
python controlmllm/task/ROC/llava_roc_visual_anchors.py \
    --model_path $MODEL_PATH \
    --data_path $DATA_PATH \
    --question_file $QUESTION_FILE \
    --answers_file $OUTPUT_FILE \
    --visual_prompt $VISUAL_PROMPT \
    --T $T \
    --alpha $ALPHA \
    --lr $LR \
    --n_spatial_anchors $N_SPATIAL \
    --n_semantic_anchors $N_SEMANTIC \
    --n_relational_anchors $N_RELATIONAL \
    --anchor_init_std $ANCHOR_INIT_STD \
    --grad_clip $GRAD_CLIP \
    $USE_EMA \
    $EARLY_STOP \
    $VERBOSE \
    $SHOW_ATT  # 取消注释以显示注意力图

echo "========================================"
echo "优化完成！结果保存到: $OUTPUT_FILE"
echo "========================================"

# 可选：运行不同配置的实验
# echo "是否运行消融实验? (y/n)"
# read -r response
# if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
#     echo "运行消融实验..."
    
#     # 实验1: 只用spatial anchors
#     echo "实验1: 只使用spatial anchors"
#     python controlmllm/task/ROC/llava_roc_visual_anchors.py \
#         --model_path $MODEL_PATH \
#         --data_path $DATA_PATH \
#         --question_file $QUESTION_FILE \
#         --answers_file "${OUTPUT_DIR}/llava_roc_visual_anchors_spatial_only_${TIMESTAMP}.json" \
#         --visual_prompt $VISUAL_PROMPT \
#         --T $T \
#         --alpha $ALPHA \
#         --lr $LR \
#         --n_spatial_anchors 3 \
#         --n_semantic_anchors 0 \
#         --n_relational_anchors 0 \
#         --anchor_init_std $ANCHOR_INIT_STD \
#         $USE_EMA $EARLY_STOP
    
#     # 实验2: 只用semantic anchors  
#     echo "实验2: 只使用semantic anchors"
#     python controlmllm/task/ROC/llava_roc_visual_anchors.py \
#         --model_path $MODEL_PATH \
#         --data_path $DATA_PATH \
#         --question_file $QUESTION_FILE \
#         --answers_file "${OUTPUT_DIR}/llava_roc_visual_anchors_semantic_only_${TIMESTAMP}.json" \
#         --visual_prompt $VISUAL_PROMPT \
#         --T $T \
#         --alpha $ALPHA \
#         --lr $LR \
#         --n_spatial_anchors 0 \
#         --n_semantic_anchors 3 \
#         --n_relational_anchors 0 \
#         --anchor_init_std $ANCHOR_INIT_STD \
#         $USE_EMA $EARLY_STOP
    
#     # 实验3: 只用relational anchors
#     echo "实验3: 只使用relational anchors"  
#     python controlmllm/task/ROC/llava_roc_visual_anchors.py \
#         --model_path $MODEL_PATH \
#         --data_path $DATA_PATH \
#         --question_file $QUESTION_FILE \
#         --answers_file "${OUTPUT_DIR}/llava_roc_visual_anchors_relational_only_${TIMESTAMP}.json" \
#         --visual_prompt $VISUAL_PROMPT \
#         --T $T \
#         --alpha $ALPHA \
#         --lr $LR \
#         --n_spatial_anchors 0 \
#         --n_semantic_anchors 0 \
#         --n_relational_anchors 3 \
#         --anchor_init_std $ANCHOR_INIT_STD \
#         $USE_EMA $EARLY_STOP
        
#     echo "消融实验完成!"
# fi 